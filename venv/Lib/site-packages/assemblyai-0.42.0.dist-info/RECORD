assemblyai-0.42.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
assemblyai-0.42.0.dist-info/LICENSE,sha256=YhtHU3ahnEZE-3mXpDVOzm0N9hJV3JrdZnCO9lIg4GM,1067
assemblyai-0.42.0.dist-info/METADATA,sha256=otvEzrtN6IpAWV3k-qwIByLMSDvg0GmN36mGiV9AtX4,27843
assemblyai-0.42.0.dist-info/RECORD,,
assemblyai-0.42.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
assemblyai-0.42.0.dist-info/WHEEL,sha256=iAkIy5fosb7FzIOwONchHf19Qu7_1wCWyFNR5gu9nU0,91
assemblyai-0.42.0.dist-info/top_level.txt,sha256=NVN58zfhMzl9LIUI6C1fvfIqnWON6uHRKtlRfW6w_QU,11
assemblyai/__init__.py,sha256=1CPhm_obLMasqXoX-oK1hOEQ66j8wf97mjuoVSKiM34,3451
assemblyai/__pycache__/__init__.cpython-310.pyc,,
assemblyai/__pycache__/__version__.cpython-310.pyc,,
assemblyai/__pycache__/api.cpython-310.pyc,,
assemblyai/__pycache__/client.cpython-310.pyc,,
assemblyai/__pycache__/extras.cpython-310.pyc,,
assemblyai/__pycache__/lemur.cpython-310.pyc,,
assemblyai/__pycache__/transcriber.cpython-310.pyc,,
assemblyai/__pycache__/types.cpython-310.pyc,,
assemblyai/__version__.py,sha256=fXDp5M3ZbGNxa2CR646QIbRo8oBi15GhGQT_3rvssIE,23
assemblyai/api.py,sha256=ZHAhXWTQa0J77KPU4whXWLjt6aLASDg9cAvurtVQBn8,12209
assemblyai/client.py,sha256=VB_6yxUNLXUKqRb7VQRuwhFU66BO8Fb7XgJs4yDjt9w,3155
assemblyai/extras.py,sha256=EYfV6ubKM8wJqUCp0pVL4miEGoJCV0F2GCSIwQ2t8c4,3784
assemblyai/lemur.py,sha256=1ddbXxN_jdQiyxAQ2TWMzxbtCwnmViQJNwqQmVhKIVI,22871
assemblyai/streaming/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
assemblyai/streaming/__pycache__/__init__.cpython-310.pyc,,
assemblyai/streaming/v3/__init__.py,sha256=og7TJrvWYeznuQWJHaZtN4baVBva_EAzovab0w9O-PE,532
assemblyai/streaming/v3/__pycache__/__init__.cpython-310.pyc,,
assemblyai/streaming/v3/__pycache__/client.cpython-310.pyc,,
assemblyai/streaming/v3/__pycache__/models.cpython-310.pyc,,
assemblyai/streaming/v3/client.py,sha256=j3aNcB3e-5n6LCQh_pMVu1IbVX8tsrB0ODiIxW0lWDc,9534
assemblyai/streaming/v3/models.py,sha256=_7TPmPDtFAPNxeLaFnWmqIHmBP7IsXpFfdAiSIiFclQ,3215
assemblyai/transcriber.py,sha256=KApOV5UvxcMQlYTsQukmHubmIwg95Pog_USyQ6n65fk,55108
assemblyai/types.py,sha256=HnaTrcdLQDk0gQjOAFWjIZJZtNKazDWmMS1p85wJHSE,80907
