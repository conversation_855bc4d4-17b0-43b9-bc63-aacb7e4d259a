"""
Vercel Serverless Function: Configuration Management
Handles API key storage and retrieval for the AI Meeting Transcription Assistant
"""

import os
import json
import secrets
from datetime import datetime
from http.server import BaseHT<PERSON>RequestHandler
import jwt

# Configuration
SECRET_KEY = os.environ.get('SECRET_KEY', secrets.token_hex(32))

# In-memory storage for demo (use database in production)
# Note: In production, use a persistent database like Vercel KV or external database
api_keys_storage = {}

def verify_session_token(token):
    """Verify and decode JWT session token"""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=['HS256'])
        return payload['user_id']
    except jwt.ExpiredSignatureError:
        return None
    except jwt.InvalidTokenError:
        return None

def require_auth(func):
    """Decorator to require authentication"""
    def wrapper(self):
        auth_header = self.headers.get('Authorization')
        if not auth_header:
            self.send_response(401)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({'error': 'No authorization token provided'}).encode())
            return
        
        token = auth_header
        if token.startswith('Bearer '):
            token = token[7:]
        
        user_id = verify_session_token(token)
        if not user_id:
            self.send_response(401)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({'error': 'Invalid or expired token'}).encode())
            return
        
        self.user_id = user_id
        return func(self)
    
    return wrapper

class handler(BaseHTTPRequestHandler):
    @require_auth
    def do_GET(self):
        """Get user's API configuration (without exposing keys)"""
        try:
            config = api_keys_storage.get(self.user_id, {})
            
            response_data = {
                'assemblyai_configured': bool(config.get('assemblyai_key')),
                'gemini_configured': bool(config.get('gemini_key')),
                'last_updated': config.get('last_updated')
            }
            
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            self.wfile.write(json.dumps(response_data).encode())
            
        except Exception as e:
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({'error': str(e)}).encode())
    
    @require_auth
    def do_POST(self):
        """Save user's API configuration"""
        try:
            content_length = int(self.headers.get('Content-Length', 0))
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode())
            
            assemblyai_key = data.get('assemblyai_key', '').strip()
            gemini_key = data.get('gemini_key', '').strip()
            
            config = {
                'last_updated': datetime.utcnow().isoformat()
            }
            
            if assemblyai_key:
                if len(assemblyai_key) > 10:
                    config['assemblyai_key'] = assemblyai_key
                else:
                    self.send_response(400)
                    self.send_header('Content-Type', 'application/json')
                    self.end_headers()
                    self.wfile.write(json.dumps({'error': 'Invalid AssemblyAI API key format'}).encode())
                    return
            
            if gemini_key:
                if len(gemini_key) > 10:
                    config['gemini_key'] = gemini_key
                else:
                    self.send_response(400)
                    self.send_header('Content-Type', 'application/json')
                    self.end_headers()
                    self.wfile.write(json.dumps({'error': 'Invalid Gemini API key format'}).encode())
                    return
            
            api_keys_storage[self.user_id] = config
            
            response_data = {
                'message': 'Configuration saved successfully',
                'assemblyai_configured': bool(config.get('assemblyai_key')),
                'gemini_configured': bool(config.get('gemini_key'))
            }
            
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            self.wfile.write(json.dumps(response_data).encode())
            
        except Exception as e:
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({'error': str(e)}).encode())
    
    def do_OPTIONS(self):
        """Handle CORS preflight"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.end_headers()
